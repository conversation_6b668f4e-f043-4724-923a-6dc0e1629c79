import { useState } from "react";
import axios from "axios";

const SECTORES = [
  "Tecnología",
  "Salud",
  "Educación",
  "Gastronomía",
  "Moda",
  "Legal",
  "Turismo",
];
const VALORES = [
  "Innovación",
  "Confianza",
  "Sostenibilidad",
  "Lujo",
  "Cercanía",
  "Accesibilidad",
];
const PUBLICOS = [
  "Juvenil",
  "Familiar",
  "Profesional",
  "Empresarial",
  "Generalista",
];
const COLORES = [
  "Azul",
  "Rojo",
  "Verde",
  "Negro",
  "Amarillo",
  "Rosa",
  "Violeta",
  "Naranja",
];

const FormPromptLogo = () => {
  const [form, setForm] = useState({
    nombre: "",
    descripcion: "",
    colores: "",
    publicoObjetivo: "",
    eslogan: "",
    valores: "",
    sector: "",
    competencia: "",
    usos: "",
  });
  const [respuesta, setRespuesta] = useState("");

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const { data } = await axios.post(
        "http://localhost:3000/api/logo-prompt",
        form
      );
      setRespuesta(data.resultado);
    } catch (err) {
      setRespuesta("Error al generar la propuesta.");
    }
  };

  return (
    <div className="max-w-3xl mx-auto p-4 space-y-6">
      <h1 className="text-2xl font-bold">Generador de Brief para Logotipo</h1>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="font-semibold">Nombre del proyecto</label>
          <input
            name="nombre"
            className="w-full p-2 border"
            onChange={handleChange}
            placeholder="Ej: JurisFlow"
          />
          <p className="text-sm text-gray-500">
            Nombre de la marca o producto a representar.
          </p>
        </div>

        <div>
          <label className="font-semibold">Descripción breve</label>
          <textarea
            name="descripcion"
            className="w-full p-2 border"
            onChange={handleChange}
            rows={3}
            placeholder="¿A qué se dedica tu proyecto?"
          />
          <p className="text-sm text-gray-500">
            Describe brevemente qué hace la marca, producto o servicio.
          </p>
        </div>

        <div>
          <label className="font-semibold">Colores deseados</label>
          <select
            name="colores"
            className="w-full p-2 border"
            onChange={handleChange}
          >
            <option value="">-- Selecciona un color predominante --</option>
            {COLORES.map((c) => (
              <option key={c}>{c}</option>
            ))}
          </select>
          <p className="text-sm text-gray-500">
            Color principal que debería tener el logo (puedes combinar después).
          </p>
        </div>

        <div>
          <label className="font-semibold">Público objetivo</label>
          <select
            name="publicoObjetivo"
            className="w-full p-2 border"
            onChange={handleChange}
          >
            <option value="">-- Selecciona una opción --</option>
            {PUBLICOS.map((p) => (
              <option key={p}>{p}</option>
            ))}
          </select>
          <p className="text-sm text-gray-500">
            ¿A quién está dirigido principalmente este proyecto?
          </p>
        </div>

        <div>
          <label className="font-semibold">Eslogan</label>
          <input
            name="eslogan"
            className="w-full p-2 border"
            onChange={handleChange}
            placeholder="Ej: Donde la ley cobra vida"
          />
          <p className="text-sm text-gray-500">
            Frase corta que acompaña al nombre (opcional).
          </p>
        </div>

        <div>
          <label className="font-semibold">Valores de la marca</label>
          <select
            name="valores"
            className="w-full p-2 border"
            onChange={handleChange}
          >
            <option value="">-- Elige un valor predominante --</option>
            {VALORES.map((v) => (
              <option key={v}>{v}</option>
            ))}
          </select>
          <p className="text-sm text-gray-500">
            Selecciona el valor principal que quieres transmitir con el logo.
          </p>
        </div>

        <div>
          <label className="font-semibold">Sector o industria</label>
          <select
            name="sector"
            className="w-full p-2 border"
            onChange={handleChange}
          >
            <option value="">-- Selecciona el sector --</option>
            {SECTORES.map((s) => (
              <option key={s}>{s}</option>
            ))}
          </select>
          <p className="text-sm text-gray-500">
            Industria o ámbito donde opera tu marca.
          </p>
        </div>

        <div>
          <label className="font-semibold">Competencia o referentes</label>
          <input
            name="competencia"
            className="w-full p-2 border"
            onChange={handleChange}
            placeholder="Ej: Canva, Adobe, Figma"
          />
          <p className="text-sm text-gray-500">
            Marcas similares o que admiras (opcional).
          </p>
        </div>

        <div>
          <label className="font-semibold">Usos previstos del logo</label>
          <input
            name="usos"
            className="w-full p-2 border"
            onChange={handleChange}
            placeholder="Web, app, redes sociales, papelería..."
          />
          <p className="text-sm text-gray-500">
            ¿Dónde se usará principalmente el logotipo?
          </p>
        </div>

        <button
          type="submit"
          className="bg-blue-600 text-white px-4 py-2 rounded"
        >
          Generar propuesta
        </button>
      </form>

      {respuesta && (
        <div className="mt-6 bg-gray-100 p-4 rounded">
          <h2 className="font-bold text-lg">Propuesta generada:</h2>
          <pre className="whitespace-pre-wrap">{respuesta}</pre>
        </div>
      )}
    </div>
  );
};

export default FormPromptLogo;
