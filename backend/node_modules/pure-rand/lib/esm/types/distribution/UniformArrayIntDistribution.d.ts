import { Distribution } from './Distribution.js';
import { RandomGenerator } from '../generator/RandomGenerator.js';
import { ArrayInt } from './internals/ArrayInt.js';
declare function uniformArrayIntDistribution(from: ArrayInt, to: ArrayInt): Distribution<ArrayInt>;
declare function uniformArrayIntDistribution(from: ArrayInt, to: ArrayInt, rng: RandomGenerator): [ArrayInt, RandomGenerator];
export { uniformArrayIntDistribution };
